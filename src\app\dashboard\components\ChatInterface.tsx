'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useConversations } from '@/contexts/ConversationContext';
import { useBalance } from '@/contexts/BalanceContext';
import { useChatMessages } from '@/hooks/useChatMessages';
import { useAIChat } from '@/hooks/useAIChat';
import { useStreamingChat } from '@/hooks/useStreamingChat';
import { useChatModel } from '@/hooks/useChatModel';
import { useSessionTime } from '@/hooks/useSessionTime';
import { useFavorites } from '@/hooks/useFavorites';
import { useChatSessions } from '@/hooks/useChatSessions';
import { getStreamingSettings } from '@/lib/settingsService';
import { createConversation, getUserConversations } from '@/lib/conversationService';
import { ChatMessage, AttachmentMetadata } from '@/lib/types/chat';
import { attachmentService } from '@/lib/attachmentService';

import ChatUpperBar from './ChatUpperBar';
import ChatDownBar from './ChatDownBar';
import MessageBubble from './MessageBubble';
import ThinkingBubble from './ThinkingBubble';
import StreamingBubble from './StreamingBubble';
import DownloadModal from './DownloadModal';
import TemporaryChat from './TemporaryChat';
import ConfirmationModal from './ConfirmationModal';

interface ChatInterfaceProps {
  onNewConversation: () => void;
}

const ChatInterface = ({ onNewConversation }: ChatInterfaceProps) => {
  const [messageInput, setMessageInput] = useState('');
  const [aiMetadata, setAiMetadata] = useState<{
    usedCoT: boolean;
    confidence: number;
    processingTime: number;
  } | null>(null);
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);
  const [webSearchEnabled, setWebSearchEnabled] = useState(false);
  const [showThinking, setShowThinking] = useState(false);
  const [favoriteStates, setFavoriteStates] = useState<Record<string, boolean>>({});
  const [streamingEnabled, setStreamingEnabled] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const [attachments, setAttachments] = useState<AttachmentMetadata[]>([]);
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    confirmText?: string;
    cancelText?: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const { user } = useAuth();
  const { refreshBalance } = useBalance();
  const {
    conversations,
    selectedConversation,
    addConversation,
    refreshConversations
  } = useConversations();

  // Hook para gerenciar tempo de sessão
  const { sessionTime } = useSessionTime({
    userId: user?.uid || null,
    chatId: selectedConversation?.id || null
  });

  // Use the chat model hook to manage model state
  const {
    selectedModel,
    setSelectedModel,
    loading: modelLoading,
    error: modelError
  } = useChatModel({
    userId: user?.uid || null,
    chatId: selectedConversation?.id || null,
    conversation: selectedConversation
  });

  const {
    messages,
    loading: messagesLoading,
    error: messagesError,
    addMessage,
    addMessageWithUsage,
    deleteMessage,
    editMessage,
    regenerateFromMessage
  } = useChatMessages({
    userId: user?.uid || null,
    chatId: selectedConversation?.id || null
  });

  const {
    generateResponseWithMetadata,
    loading: aiLoading,
    error: aiError
  } = useAIChat({
    model: selectedModel, // Use the model name as-is
    temperature: selectedConversation?.temperature || 0.7,
    maxTokens: selectedConversation?.maxTokens || 2048,
    latexInstructions: selectedConversation?.latexInstructions || false,
    webSearchEnabled: webSearchEnabled,
    chatId: selectedConversation?.id,
    systemPrompt: selectedConversation?.systemPrompt || "",
    context: selectedConversation?.context || ""
  });

  const {
    generateStreamingResponse,
    cancelStreaming,
    loading: streamingLoading,
    error: streamingError,
    isStreaming,
    streamingContent: currentStreamingContent
  } = useStreamingChat({
    model: selectedModel,
    temperature: selectedConversation?.temperature || 0.7,
    maxTokens: selectedConversation?.maxTokens || 2048,
    latexInstructions: selectedConversation?.latexInstructions || false,
    webSearchEnabled: webSearchEnabled,
    chatId: selectedConversation?.id,
    systemPrompt: selectedConversation?.systemPrompt || "",
    context: selectedConversation?.context || ""
  });

  const {
    addToFavorites,
    removeFromFavorites,
    isFavoriteSync,
    favoritesByChat,
    refreshFavorites
  } = useFavorites({
    userId: user?.uid || null
  });

  // Chat sessions management
  const {
    sessions,
    metadata: sessionMetadata,
    navigationState,
    displayMessages,
    loadNextSession,
    loadAllSessions,
    resetSessions,
    canLoadMore,
    isAllLoaded
  } = useChatSessions({
    messages
  });

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [displayMessages]);

  // Update favorite states when conversation or favorites change
  useEffect(() => {
    if (!selectedConversation || !user?.uid) {
      setFavoriteStates({});
      return;
    }

    // Get favorites for current chat and build state object
    const chatFavorites = favoritesByChat(selectedConversation.id);
    const states: Record<string, boolean> = {};

    // Mark all favorites as true
    chatFavorites.forEach(fav => {
      const favoriteKey = `${fav.chatId}_${fav.messageId}`;
      states[favoriteKey] = true;
    });

    setFavoriteStates(states);
  }, [selectedConversation?.id, user?.uid, favoritesByChat]);

  // Carregar configurações de streaming do usuário
  useEffect(() => {
    const loadStreamingSettings = async () => {
      if (user?.uid) {
        try {
          const settings = await getStreamingSettings(user.uid);
          setStreamingEnabled(settings.enabled);
        } catch (error) {
          console.error('Erro ao carregar configurações de streaming:', error);
        }
      }
    };

    loadStreamingSettings();
  }, [user?.uid]);

  // Auto scroll to bottom when thinking bubble appears or disappears
  useEffect(() => {
    if (showThinking) {
      // Scroll when thinking bubble appears
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100); // Small delay to ensure the thinking bubble is rendered
    }
  }, [showThinking]);





  const handleSendMessage = async (content: string, attachments?: any[]) => {
    if ((!content.trim() && !attachments?.length) || !selectedConversation || !user || (aiLoading && !streamingEnabled) || (streamingLoading && streamingEnabled)) return;

    try {
      // Add user message with attachments (já usa sistema otimista)
      await addMessage('user', content, attachments);

      // Prepare conversation history for AI preserving attachments from previous messages
      // IMPORTANTE: Sempre usar 'messages' (histórico completo), não 'displayMessages' (apenas sessões carregadas)
      const conversationHistory = messages;

      console.log('ChatInterface: Sending to AI with attachments:', attachments);
      console.log('ChatInterface: Conversation history with attachments:', conversationHistory);
      console.log('ChatInterface: Streaming enabled:', streamingEnabled);

      if (streamingEnabled) {
        // Usar streaming
        const result = await generateStreamingResponse(
          content,
          conversationHistory,
          attachments
        );

        if (result.success && result.response) {

          // Add AI response with usage data
          await addMessageWithUsage(
            'assistant',
            result.response,
            undefined, // no attachments for AI responses
            result.metadata?.usage, // usage data from OpenRouter
            result.metadata?.processingTime // response time
          );

          // Atualizar saldo do OpenRouter após resposta finalizada
          refreshBalance();

          // Store metadata for display
          if (result.metadata) {
            setAiMetadata({
              usedCoT: result.metadata.usedCoT,
              confidence: result.metadata.confidence,
              processingTime: result.metadata.processingTime
            });
          }
        } else if (result.wasCancelled && result.response) {
          // Se foi cancelado mas há conteúdo parcial, salvar o que foi gerado
          console.log('Salvando conteúdo parcial cancelado:', result.response);
          await addMessage('assistant', result.response);
        } else if (!result.wasCancelled) {
          // Adicionar mensagem de erro apenas se não foi cancelado
          await addMessage('assistant', 'Desculpe, não consegui processar sua mensagem no momento. Tente novamente.');
        }
      } else {
        // Usar método tradicional
        setShowThinking(true);

        const result = await generateResponseWithMetadata(
          content,
          conversationHistory,
          undefined,
          attachments
        );

        if (result.success && result.response) {
          // Esconder thinking bubble
          setShowThinking(false);

          // Add AI response with usage data
          await addMessageWithUsage(
            'assistant',
            result.response,
            undefined, // no attachments for AI responses
            result.metadata?.usage, // usage data from OpenRouter
            result.metadata?.processingTime // response time
          );

          // Atualizar saldo do OpenRouter após resposta finalizada
          refreshBalance();

          // Store metadata for display
          if (result.metadata) {
            setAiMetadata({
              usedCoT: result.metadata.usedCoT,
              confidence: result.metadata.confidence,
              processingTime: result.metadata.processingTime
            });
          }

          // Force scroll to bottom after AI response
          setTimeout(() => {
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
          }, 100);
        } else {
          // Esconder thinking bubble e adicionar mensagem de erro
          setShowThinking(false);
          await addMessage('assistant', 'Desculpe, não consegui processar sua mensagem no momento. Tente novamente.');

          // Force scroll to bottom after error message
          setTimeout(() => {
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
          }, 100);
        }
      }

      // Force scroll to bottom after any response
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      // Esconder thinking bubble
      setShowThinking(false);
      // Add error message
      await addMessage('assistant', 'Ocorreu um erro ao processar sua mensagem. Verifique sua configuração de IA.');

      // Force scroll to bottom after error message
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  };

  const handleMessageAction = async (messageId: string, action: 'delete' | 'edit' | 'copy' | 'regenerate' | 'favorite' | 'unfavorite') => {
    const message = messages.find(m => m.id === messageId);
    if (!message) return;

    switch (action) {
      case 'copy':
        try {
          await navigator.clipboard.writeText(message.content);
          // Você pode adicionar uma notificação de sucesso aqui se desejar
        } catch (error) {
          console.error('Erro ao copiar mensagem:', error);
        }
        break;

      case 'delete':
        setConfirmationModal({
          isOpen: true,
          title: 'Deletar Mensagem',
          message: 'Tem certeza que deseja deletar esta mensagem? Esta ação não pode ser desfeita.',
          onConfirm: () => handleDeleteMessage(messageId),
          confirmText: 'Deletar',
          cancelText: 'Cancelar'
        });
        break;

      case 'edit':
        // A edição agora é feita diretamente no MessageBubble
        break;

      case 'regenerate':
        if (message.role === 'user') {
          setConfirmationModal({
            isOpen: true,
            title: 'Regenerar Resposta',
            message: 'Isso irá excluir todas as mensagens após esta e gerar uma nova resposta. Deseja continuar?',
            onConfirm: () => handleRegenerateFromMessage(messageId),
            confirmText: 'Regenerar',
            cancelText: 'Cancelar'
          });
        }
        break;

      case 'favorite':
        try {
          if (selectedConversation) {
            await addToFavorites(messageId, selectedConversation.id, selectedConversation.name, message);
            // The state will be updated automatically by the useEffect that watches favoritesByChat
            await refreshFavorites();
          }
        } catch (error) {
          console.error('Erro ao adicionar aos favoritos:', error);
        }
        break;

      case 'unfavorite':
        try {
          if (selectedConversation) {
            await removeFromFavorites(messageId, selectedConversation.id);
            // The state will be updated automatically by the useEffect that watches favoritesByChat
            await refreshFavorites();
          }
        } catch (error) {
          console.error('Erro ao remover dos favoritos:', error);
        }
        break;
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    try {
      await deleteMessage(messageId);
    } catch (error) {
      console.error('Erro ao deletar mensagem:', error);
    } finally {
      setConfirmationModal(prev => ({ ...prev, isOpen: false }));
    }
  };

  const handleEditMessage = async (messageId: string, content: string) => {
    try {
      await editMessage(messageId, content);
    } catch (error) {
      console.error('Erro ao editar mensagem:', error);
    }
  };

  // Funções de scroll
  const scrollToTop = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  // Handlers para drag and drop de arquivos
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter(prev => prev + 1);
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter(prev => prev - 1);
    if (dragCounter <= 1) {
      setIsDragOver(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    setDragCounter(0);

    if (!selectedConversation || !user) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // Filtrar apenas arquivos suportados
    const supportedFiles = files.filter(file => {
      const supportedTypes = ['image/png', 'image/jpeg', 'image/webp', 'application/pdf'];
      return supportedTypes.includes(file.type);
    });

    if (supportedFiles.length === 0) {
      console.warn('Nenhum arquivo suportado foi encontrado');
      return;
    }

    if (supportedFiles.length !== files.length) {
      console.warn(`${files.length - supportedFiles.length} arquivo(s) não suportado(s) foram ignorados`);
    }

    try {
      // Usar o mesmo serviço de upload que o ChatDownBar usa
      const result = await attachmentService.uploadMultipleFiles(
        user.uid,
        selectedConversation.id,
        supportedFiles,
        selectedConversation.name
      );

      if (result.success && result.attachments.length > 0) {
        // Apenas adicionar aos anexos, sem enviar mensagem
        setAttachments(prev => [...prev, ...result.attachments]);
        console.log('Files uploaded and added to attachments:', result.attachments);
      }

      if (result.errors.length > 0) {
        console.error('Upload errors:', result.errors);
        // TODO: Mostrar toast de erro
      }
    } catch (error) {
      console.error('Error uploading dropped files:', error);
      // TODO: Mostrar toast de erro
    }
  };

  // Função para gerenciar anexos
  const handleAttachmentsChange = (newAttachments: AttachmentMetadata[]) => {
    setAttachments(newAttachments);
  };

  const handleRegenerateFromMessage = async (messageId: string) => {
    try {
      const userMessage = messages.find(m => m.id === messageId);
      if (!userMessage || userMessage.role !== 'user') {
        console.error('Mensagem do usuário não encontrada ou inválida');
        return;
      }

      console.log('Iniciando regeneração para mensagem:', messageId);

      // Deletar todas as mensagens após a mensagem do usuário
      await regenerateFromMessage(messageId);
      console.log('Mensagens deletadas com sucesso');

      // Gerar nova resposta
      // IMPORTANTE: Usar 'messages' (histórico completo) para contexto, não 'displayMessages'
      const contextMessages = messages.filter(m => m.timestamp < userMessage.timestamp);
      console.log('Contexto preparado:', contextMessages.length, 'mensagens');
      console.log('Regeneração com streaming enabled:', streamingEnabled);

      if (streamingEnabled) {
        // Usar streaming para regeneração
        const result = await generateStreamingResponse(
          userMessage.content,
          contextMessages,
          userMessage.attachments,
          {
            model: selectedModel,
            temperature: selectedConversation?.temperature || 0.7,
            maxTokens: selectedConversation?.maxTokens || 2048,
            latexInstructions: selectedConversation?.latexInstructions || false,
            webSearchEnabled: webSearchEnabled
          }
        );

        if (result.success && result.response) {

          // Add AI response with usage data
          await addMessageWithUsage(
            'assistant',
            result.response,
            undefined, // no attachments for AI responses
            result.metadata?.usage, // usage data from OpenRouter
            result.metadata?.processingTime // response time
          );

          // Atualizar saldo do OpenRouter após resposta finalizada
          refreshBalance();

          // Store metadata for display
          if (result.metadata) {
            setAiMetadata({
              usedCoT: result.metadata.usedCoT,
              confidence: result.metadata.confidence,
              processingTime: result.metadata.processingTime
            });
          }
          console.log('Nova resposta com streaming adicionada com sucesso');
        } else if (result.wasCancelled && result.response) {
          // Se foi cancelado mas há conteúdo parcial, salvar o que foi gerado
          console.log('Salvando conteúdo parcial cancelado na regeneração:', result.response);
          await addMessage('assistant', result.response);
        } else if (!result.wasCancelled) {
          // Adicionar mensagem de erro apenas se não foi cancelado
          await addMessage('assistant', 'Desculpe, não consegui regenerar a resposta no momento. Tente novamente.');
        }
      } else {
        // Usar método tradicional
        setShowThinking(true);

        const aiResponse = await generateResponseWithMetadata(
          userMessage.content,
          contextMessages,
          {
            model: selectedModel,
            temperature: selectedConversation?.temperature || 0.7,
            maxTokens: selectedConversation?.maxTokens || 2048,
            latexInstructions: selectedConversation?.latexInstructions || false,
            webSearchEnabled: webSearchEnabled
          },
          userMessage.attachments // Include attachments from the user message being regenerated
        );

        console.log('Resposta da IA:', aiResponse);

        if (aiResponse.success && aiResponse.response) {
          // Esconder thinking bubble
          setShowThinking(false);
          await addMessageWithUsage(
            'assistant',
            aiResponse.response,
            undefined, // no attachments for AI responses
            aiResponse.metadata?.usage, // usage data from OpenRouter
            aiResponse.metadata?.processingTime // response time
          );

          // Atualizar saldo do OpenRouter após resposta finalizada
          refreshBalance();

          setAiMetadata(aiResponse.metadata || null);
          console.log('Nova resposta adicionada com sucesso');
        } else {
          console.error('Falha na geração da resposta da IA');
          // Esconder thinking bubble e adicionar mensagem de erro
          setShowThinking(false);
          await addMessage('assistant', 'Desculpe, não consegui regenerar a resposta no momento. Tente novamente.');
        }
      }
    } catch (error) {
      console.error('Erro ao regenerar mensagem:', error);
      // Esconder thinking bubble
      setShowThinking(false);
      await addMessage('assistant', 'Ocorreu um erro ao regenerar a resposta. Verifique sua configuração de IA.');
    } finally {
      setConfirmationModal(prev => ({ ...prev, isOpen: false }));
    }
  };

  // Função para criar chat permanente a partir do temporário
  const handleCreatePermanentChat = async (temporaryMessages: ChatMessage[]) => {
    if (!user || temporaryMessages.length === 0) return;

    try {
      // Criar nova conversa
      const chatName = `Chat ${new Date().toLocaleDateString('pt-BR')} ${new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`;

      const newChatId = await createConversation(user.uid, chatName, {
        systemPrompt: '',
        context: '',
        temperature: 0.7,
        maxTokens: 2048,
        latexInstructions: false
      });

      console.log('Chat permanente criado:', newChatId);
      console.log('Transferindo mensagens temporárias:', temporaryMessages);

      // Adicionar todas as mensagens temporárias ao chat permanente
      for (const message of temporaryMessages) {
        try {
          const response = await fetch('/api/chat/message', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user.uid,
              chatId: newChatId,
              role: message.role,
              content: message.content,
              attachments: message.attachments,
              usage: message.usage,
              responseTime: message.responseTime,
              typingSpeed: message.typingSpeed
            }),
          });

          const data = await response.json();
          if (!data.success) {
            console.error('Erro ao adicionar mensagem:', data.error);
          }
        } catch (error) {
          console.error('Erro ao transferir mensagem:', error);
        }
      }

      // Buscar a conversa recém-criada
      const userConversations = await getUserConversations(user.uid, false);
      const newConversation = userConversations.find(conv => conv.id === newChatId);

      if (newConversation) {
        // Adicionar conversa ao contexto (isso a selecionará automaticamente)
        addConversation(newConversation);
        console.log('Chat permanente criado e mensagens transferidas com sucesso!');
      } else {
        // Fallback: atualizar lista completa
        await refreshConversations();
      }
    } catch (error) {
      console.error('Erro ao criar chat permanente:', error);
    }
  };

  // Show empty state if no conversations
  if (conversations.length === 0) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <div className="text-center space-y-8 max-w-2xl mx-auto px-6">
          <div className="space-y-6">
            <h1 className="text-6xl neon-text mb-6 tracking-tight font-display floating">
              RafthorIA
            </h1>
            <p className="text-xl text-indigo-200 max-w-2xl mx-auto leading-relaxed">
              Sua assistente de IA inteligente. Como posso te ajudar hoje?
            </p>
            <div className="mt-12 w-24 h-1 mx-auto bg-gradient-to-r from-transparent via-indigo-500/50 to-transparent"></div>
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={onNewConversation}
              className="glow-button group relative overflow-hidden px-8 py-4"
            >
              <span className="relative z-10 flex items-center font-medium">
                <svg className="w-5 h-5 mr-2 transition-transform duration-500 ease-out group-hover:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Criar Nova Conversa
              </span>
              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-600/20 to-indigo-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show temporary chat when no conversation is selected
  if (!selectedConversation) {
    return <TemporaryChat onCreatePermanentChat={handleCreatePermanentChat} />;
  }

  return (
    <div
      className="flex-1 flex flex-col h-screen bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 relative w-full max-w-full overflow-hidden chat-interface-container"
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>

      {/* Drag and Drop Overlay */}
      {isDragOver && (
        <div className="absolute inset-0 bg-blue-600/20 backdrop-blur-sm border-2 border-dashed border-blue-400 z-50 flex items-center justify-center">
          <div className="bg-blue-900/90 backdrop-blur-sm border border-blue-500/50 rounded-2xl p-8 text-center shadow-2xl">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-500/20 flex items-center justify-center">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Solte os arquivos aqui</h3>
            <p className="text-blue-300/80 text-sm">
              Suportamos imagens (PNG, JPEG, WebP) e PDFs
            </p>
          </div>
        </div>
      )}
      {/* Upper Bar */}
      <div className="flex-shrink-0">
        <ChatUpperBar
          chatName={selectedConversation.name}
          currentModel={selectedModel}
          sessionTime={sessionTime}
          isLoading={false} // Nunca mostrar loading no nome do chat
          onDownloadClick={() => setIsDownloadModalOpen(true)}
          aiMetadata={aiMetadata}
          chatId={selectedConversation.id}
        />
      </div>

      {/* Messages Area */}
      <div ref={messagesContainerRef} className="flex-1 overflow-y-auto overflow-x-hidden px-6 py-8 space-y-6 min-h-0 relative">
        {/* Fundo sutil para a área de mensagens */}
        <div className="absolute inset-0 bg-gradient-to-b from-blue-950/20 via-transparent to-blue-950/20 pointer-events-none"></div>
        {messagesLoading && (
          <div className="flex justify-center relative z-10">
            <div className="flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"></div>
              <span className="text-blue-200 text-sm font-medium">Carregando mensagens...</span>
            </div>
          </div>
        )}

        {(messagesError || aiError || modelError) && (
          <div className="bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 mb-4 relative z-10">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center">
                <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-red-300 text-sm font-medium">{messagesError || aiError || modelError}</p>
            </div>
          </div>
        )}

        {messages.length === 0 && !messagesLoading && (
          <div className="flex items-center justify-center h-full relative z-10">
            <div className="text-center">
              <div className="mb-4">
                <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-600/20 flex items-center justify-center border border-blue-500/30">
                  <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
              </div>
              <p className="text-lg text-blue-200 font-medium">Inicie uma conversa</p>
              <p className="text-sm text-blue-300/70 mt-1">Envie uma mensagem para começar</p>
            </div>
          </div>
        )}

        {/* Session Loading Buttons */}
        {canLoadMore && displayMessages.length > 0 && (
          <div className="flex flex-col gap-2 mb-4 px-4">
            <div className="flex justify-center">
              <div className="bg-blue-900/30 backdrop-blur-sm border border-blue-700/30 rounded-lg p-3">
                <div className="flex items-center gap-3 text-sm text-blue-300 mb-3">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span>
                    Sessão {navigationState.loadedSessions.size} de {sessionMetadata.totalSessions} carregada
                  </span>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={loadNextSession}
                    className="px-3 py-2 bg-blue-600/70 hover:bg-blue-500/70 text-white text-sm rounded-lg transition-colors duration-200 flex items-center gap-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                    Carregar mais sessão
                  </button>
                  <button
                    onClick={loadAllSessions}
                    className="px-3 py-2 bg-blue-700/70 hover:bg-blue-600/70 text-white text-sm rounded-lg transition-colors duration-200 flex items-center gap-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                    </svg>
                    Carregar todas sessões
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {displayMessages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            onAction={handleMessageAction}
            onEditSave={handleEditMessage}
            isFavorite={selectedConversation ? favoriteStates[`${selectedConversation.id}_${message.id}`] || false : false}
          />
        ))}

        {/* Thinking Bubble */}
        {showThinking && !streamingEnabled && (
          <ThinkingBubble model={selectedModel} />
        )}

        {/* Streaming Bubble */}
        {isStreaming && streamingEnabled && (
          <StreamingBubble
            content={currentStreamingContent}
            model={selectedModel}
            isStreaming={isStreaming}
          />
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Down Bar */}
      <div className="flex-shrink-0">
        <ChatDownBar
          messageInput={messageInput}
          setMessageInput={setMessageInput}
          onSendMessage={handleSendMessage}
          isLoading={(streamingEnabled ? streamingLoading : aiLoading) || modelLoading}
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          webSearchEnabled={webSearchEnabled}
          onWebSearchToggle={setWebSearchEnabled}
          chatId={selectedConversation?.id}
          chatName={selectedConversation?.name}
          onScrollToTop={scrollToTop}
          onScrollToBottom={scrollToBottom}
          isStreaming={isStreaming && streamingEnabled}
          onCancelStreaming={cancelStreaming}
          externalAttachments={attachments}
          onAttachmentsChange={handleAttachmentsChange}
        />
      </div>

      {/* Download Modal */}
      <DownloadModal
        isOpen={isDownloadModalOpen}
        onClose={() => setIsDownloadModalOpen(false)}
        messages={messages}
        chatName={selectedConversation?.name || 'Chat'}
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        title={confirmationModal.title}
        message={confirmationModal.message}
        onConfirm={confirmationModal.onConfirm}
        onCancel={() => setConfirmationModal(prev => ({ ...prev, isOpen: false }))}
        confirmText={confirmationModal.confirmText}
        cancelText={confirmationModal.cancelText}
      />


    </div>
  );
};

export default ChatInterface;

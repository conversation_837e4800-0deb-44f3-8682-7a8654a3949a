'use client';

import { useState, useRef, useEffect } from 'react';
import { AttachmentMetadata } from '@/lib/types/chat';
import { attachmentService } from '@/lib/attachmentService';
import { useAuth } from '@/hooks/useAuth';

interface AttachmentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  chatId: string;
  chatName: string;
  historyAttachments: AttachmentMetadata[]; // Anexos do histórico de mensagens
  currentAttachments: AttachmentMetadata[]; // Anexos atuais (não enviados)
  onAttachmentsChange: (attachments: AttachmentMetadata[]) => void;
}

export default function AttachmentsModal({
  isOpen,
  onClose,
  chatId,
  chatName,
  historyAttachments,
  currentAttachments,
  onAttachmentsChange
}: AttachmentsModalProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'current' | 'history'>('current');
  const [previewAttachment, setPreviewAttachment] = useState<AttachmentMetadata | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'image' | 'pdf'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'size'>('date');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuth();

  // Fechar modal com ESC
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || !user) return;

    setIsUploading(true);
    try {
      const fileArray = Array.from(files);
      const result = await attachmentService.uploadMultipleFiles(
        user.uid,
        chatId,
        fileArray,
        chatName
      );

      if (result.success && result.attachments.length > 0) {
        onAttachmentsChange([...currentAttachments, ...result.attachments]);
      }

      if (result.errors.length > 0) {
        console.error('Upload errors:', result.errors);
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error uploading files:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveAttachment = (attachmentId: string) => {
    const updatedAttachments = currentAttachments.filter(att => att.id !== attachmentId);
    onAttachmentsChange(updatedAttachments);
  };

  // Drag and drop handlers
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Função para filtrar anexos
  const filterAttachments = (attachments: AttachmentMetadata[]): AttachmentMetadata[] => {
    let filtered = attachments;

    // Aplicar filtro por tipo
    if (filterType !== 'all') {
      filtered = filtered.filter(att => att.type === filterType);
    }

    // Aplicar ordenação
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.filename.localeCompare(b.filename);
        case 'size':
          return b.size - a.size;
        case 'date':
        default:
          return b.uploadedAt - a.uploadedAt;
      }
    });

    return filtered;
  };

  const renderAttachmentCard = (attachment: AttachmentMetadata, isFromHistory: boolean = false) => (
    <div key={attachment.id} className="bg-blue-800/30 border border-blue-600/30 rounded-lg p-3 sm:p-4 hover:bg-blue-700/30 transition-colors">
      <div className="flex items-start space-x-3">
        {/* Preview/Icon */}
        <div className="flex-shrink-0">
          {attachment.type === 'image' ? (
            <img
              src={attachment.url}
              alt={attachment.filename}
              className="w-10 h-10 sm:w-12 sm:h-12 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => setPreviewAttachment(attachment)}
            />
          ) : (
            <div
              className="w-10 h-10 sm:w-12 sm:h-12 bg-red-500/20 rounded flex items-center justify-center cursor-pointer hover:bg-red-500/30 transition-colors"
              onClick={() => setPreviewAttachment(attachment)}
            >
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          )}
        </div>

        {/* Info */}
        <div className="flex-1 min-w-0">
          <h4 className="text-xs sm:text-sm font-medium text-white truncate" title={attachment.filename}>
            {attachment.filename}
          </h4>
          <p className="text-xs text-blue-300/70 mt-1">
            {formatFileSize(attachment.size)} • {formatDate(attachment.uploadedAt)}
          </p>
          {isFromHistory && (
            <p className="text-xs text-blue-400/60 mt-1">
              Do histórico do chat
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          <button
            onClick={() => window.open(attachment.url, '_blank')}
            className="p-1.5 text-blue-300 hover:text-blue-200 hover:bg-blue-600/30 rounded transition-colors"
            title="Abrir"
          >
            <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </button>

          {!isFromHistory && (
            <button
              onClick={() => handleRemoveAttachment(attachment.id)}
              className="p-1.5 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded transition-colors"
              title="Remover"
            >
              <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4 animate-in fade-in duration-200">
      <div className="bg-blue-900/95 border border-blue-700/30 rounded-lg sm:rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in zoom-in-95 duration-200">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-blue-700/30">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg sm:text-xl font-semibold text-white truncate">Anexos do Chat</h2>
            <p className="text-xs sm:text-sm text-blue-300/70 mt-1 truncate" title={chatName}>{chatName}</p>
          </div>

          <button
            onClick={onClose}
            className="p-2 text-blue-300 hover:text-white hover:bg-blue-700/30 rounded-lg transition-colors flex-shrink-0 ml-4"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-blue-700/30">
          <button
            onClick={() => setSelectedTab('current')}
            className={`flex-1 px-3 sm:px-6 py-3 text-xs sm:text-sm font-medium transition-colors ${
              selectedTab === 'current'
                ? 'text-white bg-blue-700/30 border-b-2 border-blue-400'
                : 'text-blue-300/70 hover:text-blue-200 hover:bg-blue-800/20'
            }`}
          >
            <span className="hidden sm:inline">Anexos Atuais</span>
            <span className="sm:hidden">Atuais</span>
            <span className="ml-1">({currentAttachments.length})</span>
          </button>
          <button
            onClick={() => setSelectedTab('history')}
            className={`flex-1 px-3 sm:px-6 py-3 text-xs sm:text-sm font-medium transition-colors ${
              selectedTab === 'history'
                ? 'text-white bg-blue-700/30 border-b-2 border-blue-400'
                : 'text-blue-300/70 hover:text-blue-200 hover:bg-blue-800/20'
            }`}
          >
            <span className="hidden sm:inline">Histórico</span>
            <span className="sm:hidden">Hist.</span>
            <span className="ml-1">({historyAttachments.length})</span>
          </button>
        </div>

        {/* Filters and Sort */}
        <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-blue-700/30 bg-blue-900/20">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <div className="flex items-center gap-2">
                <span className="text-xs sm:text-sm text-blue-300/70 whitespace-nowrap">Filtrar:</span>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as 'all' | 'image' | 'pdf')}
                  className="bg-blue-800/50 border border-blue-600/30 rounded px-2 py-1 text-xs sm:text-sm text-white focus:outline-none focus:border-blue-500 flex-1 sm:flex-none"
                >
                  <option value="all">Todos</option>
                  <option value="image">Imagens</option>
                  <option value="pdf">PDFs</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-xs sm:text-sm text-blue-300/70 whitespace-nowrap">Ordenar:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'name' | 'size')}
                  className="bg-blue-800/50 border border-blue-600/30 rounded px-2 py-1 text-xs sm:text-sm text-white focus:outline-none focus:border-blue-500 flex-1 sm:flex-none"
                >
                  <option value="date">Data</option>
                  <option value="name">Nome</option>
                  <option value="size">Tamanho</option>
                </select>
              </div>
            </div>

            <div className="text-xs sm:text-sm text-blue-300/70 text-center sm:text-right">
              {selectedTab === 'current'
                ? `${filterAttachments(currentAttachments).length} anexo(s)`
                : `${filterAttachments(historyAttachments).length} anexo(s)`
              }
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 max-h-[45vh] sm:max-h-[50vh] overflow-y-auto">
          {selectedTab === 'current' && (
            <div className="space-y-4">
              {/* Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  isDragOver
                    ? 'border-blue-400 bg-blue-500/10'
                    : 'border-blue-600/30 hover:border-blue-500/50'
                }`}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*,.pdf"
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="hidden"
                />

                <svg className="w-12 h-12 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>

                <p className="text-white font-medium mb-2">
                  {isUploading ? 'Enviando arquivos...' : isDragOver ? 'Solte os arquivos aqui' : 'Adicionar novos anexos'}
                </p>
                <p className="text-blue-300/70 text-sm mb-4">
                  {isDragOver ? 'Solte para fazer upload' : 'Arraste arquivos aqui ou clique para selecionar'}
                </p>

                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  {isUploading ? 'Enviando...' : 'Selecionar Arquivos'}
                </button>
              </div>

              {/* Current Attachments */}
              {(() => {
                const filteredAttachments = filterAttachments(currentAttachments);
                return filteredAttachments.length > 0 ? (
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium text-white">Anexos Prontos para Envio</h3>
                    {filteredAttachments.map(attachment => renderAttachmentCard(attachment, false))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-blue-300/70">
                      {currentAttachments.length === 0
                        ? 'Nenhum anexo adicionado ainda'
                        : 'Nenhum anexo corresponde aos filtros selecionados'
                      }
                    </p>
                  </div>
                );
              })()}
            </div>
          )}

          {selectedTab === 'history' && (
            <div className="space-y-4">
              {(() => {
                const filteredAttachments = filterAttachments(historyAttachments);
                return filteredAttachments.length > 0 ? (
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium text-white">Anexos do Histórico</h3>
                    {filteredAttachments.map(attachment => renderAttachmentCard(attachment, true))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-blue-300/70">
                      {historyAttachments.length === 0
                        ? 'Nenhum anexo encontrado no histórico'
                        : 'Nenhum anexo corresponde aos filtros selecionados'
                      }
                    </p>
                  </div>
                );
              })()}
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {previewAttachment && (
        <div className="fixed inset-0 bg-black/80 z-60 flex items-center justify-center p-2 sm:p-4 animate-in fade-in duration-200">
          <div className="bg-blue-900/95 border border-blue-700/30 rounded-lg sm:rounded-xl max-w-4xl max-h-[95vh] sm:max-h-[90vh] w-full overflow-hidden animate-in zoom-in-95 duration-200">
            <div className="flex items-center justify-between p-3 sm:p-4 border-b border-blue-700/30">
              <h3 className="text-base sm:text-lg font-semibold text-white truncate pr-4" title={previewAttachment.filename}>
                {previewAttachment.filename}
              </h3>
              <button
                onClick={() => setPreviewAttachment(null)}
                className="p-2 text-blue-300 hover:text-white hover:bg-blue-700/30 rounded-lg transition-colors flex-shrink-0"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-3 sm:p-6 overflow-auto max-h-[calc(95vh-80px)] sm:max-h-[calc(90vh-120px)]">
              {previewAttachment.type === 'image' ? (
                <img
                  src={previewAttachment.url}
                  alt={previewAttachment.filename}
                  className="max-w-full max-h-full object-contain mx-auto rounded-lg shadow-lg"
                />
              ) : (
                <div className="text-center py-8 sm:py-12">
                  <svg className="w-12 h-12 sm:w-16 sm:h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="text-white mb-2 text-sm sm:text-base">Visualização de PDF</p>
                  <button
                    onClick={() => window.open(previewAttachment.url, '_blank')}
                    className="px-3 py-2 sm:px-4 sm:py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors text-sm sm:text-base"
                  >
                    Abrir PDF
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

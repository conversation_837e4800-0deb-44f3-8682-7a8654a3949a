[debug] [2025-07-15T01:06:41.924Z] ----------------------------------------------------------------------
[debug] [2025-07-15T01:06:41.926Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-15T01:06:41.927Z] CLI Version:   14.8.0
[debug] [2025-07-15T01:06:41.927Z] Platform:      win32
[debug] [2025-07-15T01:06:41.927Z] Node Version:  v22.14.0
[debug] [2025-07-15T01:06:41.927Z] Time:          Mon Jul 14 2025 22:06:41 GMT-0300 (<PERSON><PERSON><PERSON><PERSON>ras<PERSON>)
[debug] [2025-07-15T01:06:41.928Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-15T01:06:41.929Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-15T01:06:42.147Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-15T01:06:42.147Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-15T01:06:42.156Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-15T01:06:42.156Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-15T01:06:42.161Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-rafthoria.json
[debug] [2025-07-15T01:06:42.175Z] Checked if tokens are valid: false, expires at: 1750622073776
[debug] [2025-07-15T01:06:42.175Z] Checked if tokens are valid: false, expires at: 1750622073776
[debug] [2025-07-15T01:06:42.175Z] > refreshing access token with scopes: []
[debug] [2025-07-15T01:06:42.176Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-15T01:06:42.176Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-15T01:06:42.412Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-15T01:06:42.412Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-15T01:06:42.417Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria [none]
[debug] [2025-07-15T01:06:42.524Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-15T01:06:42.524Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-07-15T01:06:42.868Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria 200
[debug] [2025-07-15T01:06:42.868Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria {"projectId":"rafthoria","projectNumber":"437242713555","displayName":"RafthorIA","name":"projects/rafthoria","resources":{"hostingSite":"rafthoria","realtimeDatabaseInstance":"rafthoria-default-rtdb"},"state":"ACTIVE","etag":"1_d0a743d0-61bf-4807-a36d-d6ce50392ab5"}
[debug] [2025-07-15T01:06:42.874Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-15T01:06:42.874Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-15T01:06:42.874Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-15T01:06:42.874Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-15T01:06:42.907Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\henriquelenz16_gmail_com_application_default_credentials.json
[debug] [2025-07-15T01:06:42.909Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\henriquelenz16_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\henriquelenz16_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-15T01:06:42.909Z] Checked if tokens are valid: true, expires at: 1752545201412
[debug] [2025-07-15T01:06:42.909Z] Checked if tokens are valid: true, expires at: 1752545201412
[debug] [2025-07-15T01:06:42.909Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria/adminSdkConfig [none]
[debug] [2025-07-15T01:06:43.964Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria/adminSdkConfig 200
[debug] [2025-07-15T01:06:43.965Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/rafthoria/adminSdkConfig {"projectId":"rafthoria","databaseURL":"https://rafthoria-default-rtdb.firebaseio.com","storageBucket":"rafthoria.firebasestorage.app"}
[info] i  ui: downloading ui-v1.15.0.zip... {"metadata":{"emulator":{"name":"ui"},"message":"downloading ui-v1.15.0.zip..."}}
[debug] [2025-07-15T01:06:43.984Z] >>> [apiv2][query] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip 
[debug] [2025-07-15T01:06:44.263Z] <<< [apiv2][status] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip 200
[debug] [2025-07-15T01:06:44.263Z] <<< [apiv2][body] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip [stream]
[debug] [2025-07-15T01:06:44.502Z] Data is 3538469
[debug] [2025-07-15T01:06:44.502Z] [unzip] Entry: client/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-15T01:06:44.502Z] [unzip] Processing entry: client\
[debug] [2025-07-15T01:06:44.502Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\
[debug] [2025-07-15T01:06:44.503Z] [unzip] Entry: client/favicon-16x16.png (compressed_size=293 bytes, uncompressed_size=293 bytes)
[debug] [2025-07-15T01:06:44.503Z] [unzip] Processing entry: client\favicon-16x16.png
[debug] [2025-07-15T01:06:44.503Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.504Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\favicon-16x16.png
[debug] [2025-07-15T01:06:44.504Z] [unzip] Entry: client/safari-pinned-tab.svg (compressed_size=1433 bytes, uncompressed_size=2611 bytes)
[debug] [2025-07-15T01:06:44.504Z] [unzip] Processing entry: client\safari-pinned-tab.svg
[debug] [2025-07-15T01:06:44.504Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.505Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\safari-pinned-tab.svg
[debug] [2025-07-15T01:06:44.508Z] [unzip] Entry: client/favicon.ico (compressed_size=2933 bytes, uncompressed_size=13294 bytes)
[debug] [2025-07-15T01:06:44.508Z] [unzip] Processing entry: client\favicon.ico
[debug] [2025-07-15T01:06:44.508Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.508Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\favicon.ico
[debug] [2025-07-15T01:06:44.510Z] [unzip] Entry: client/index.html (compressed_size=1364 bytes, uncompressed_size=3071 bytes)
[debug] [2025-07-15T01:06:44.510Z] [unzip] Processing entry: client\index.html
[debug] [2025-07-15T01:06:44.510Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.510Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\index.html
[debug] [2025-07-15T01:06:44.511Z] [unzip] Entry: client/android-chrome-192x192.png (compressed_size=2684 bytes, uncompressed_size=2684 bytes)
[debug] [2025-07-15T01:06:44.512Z] [unzip] Processing entry: client\android-chrome-192x192.png
[debug] [2025-07-15T01:06:44.512Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.512Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\android-chrome-192x192.png
[debug] [2025-07-15T01:06:44.512Z] [unzip] Entry: client/apple-touch-icon.png (compressed_size=2123 bytes, uncompressed_size=2152 bytes)
[debug] [2025-07-15T01:06:44.513Z] [unzip] Processing entry: client\apple-touch-icon.png
[debug] [2025-07-15T01:06:44.513Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.513Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\apple-touch-icon.png
[debug] [2025-07-15T01:06:44.514Z] [unzip] Entry: client/android-chrome-512x512.png (compressed_size=5369 bytes, uncompressed_size=5411 bytes)
[debug] [2025-07-15T01:06:44.514Z] [unzip] Processing entry: client\android-chrome-512x512.png
[debug] [2025-07-15T01:06:44.514Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.514Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\android-chrome-512x512.png
[debug] [2025-07-15T01:06:44.515Z] [unzip] Entry: client/manifest.json (compressed_size=245 bytes, uncompressed_size=551 bytes)
[debug] [2025-07-15T01:06:44.515Z] [unzip] Processing entry: client\manifest.json
[debug] [2025-07-15T01:06:44.515Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.516Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\manifest.json
[debug] [2025-07-15T01:06:44.517Z] [unzip] Entry: client/robots.txt (compressed_size=26 bytes, uncompressed_size=26 bytes)
[debug] [2025-07-15T01:06:44.518Z] [unzip] Processing entry: client\robots.txt
[debug] [2025-07-15T01:06:44.518Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.518Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\robots.txt
[debug] [2025-07-15T01:06:44.518Z] [unzip] Entry: client/mstile-150x150.png (compressed_size=2034 bytes, uncompressed_size=2034 bytes)
[debug] [2025-07-15T01:06:44.519Z] [unzip] Processing entry: client\mstile-150x150.png
[debug] [2025-07-15T01:06:44.519Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.519Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\mstile-150x150.png
[debug] [2025-07-15T01:06:44.520Z] [unzip] Entry: client/assets/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-15T01:06:44.520Z] [unzip] Processing entry: client\assets\
[debug] [2025-07-15T01:06:44.520Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\
[debug] [2025-07-15T01:06:44.520Z] [unzip] Entry: client/assets/index-BX-A56oj.js (compressed_size=582089 bytes, uncompressed_size=2092775 bytes)
[debug] [2025-07-15T01:06:44.520Z] [unzip] Processing entry: client\assets\index-BX-A56oj.js
[debug] [2025-07-15T01:06:44.520Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets
[debug] [2025-07-15T01:06:44.521Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\index-BX-A56oj.js
[debug] [2025-07-15T01:06:44.533Z] [unzip] Entry: client/assets/index-CjB9C900.css (compressed_size=35979 bytes, uncompressed_size=298654 bytes)
[debug] [2025-07-15T01:06:44.533Z] [unzip] Processing entry: client\assets\index-CjB9C900.css
[debug] [2025-07-15T01:06:44.533Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets
[debug] [2025-07-15T01:06:44.534Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\index-CjB9C900.css
[debug] [2025-07-15T01:06:44.537Z] [unzip] Entry: client/assets/index-BX-A56oj.js.map (compressed_size=2362482 bytes, uncompressed_size=10036679 bytes)
[debug] [2025-07-15T01:06:44.537Z] [unzip] Processing entry: client\assets\index-BX-A56oj.js.map
[debug] [2025-07-15T01:06:44.537Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets
[debug] [2025-07-15T01:06:44.537Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\index-BX-A56oj.js.map
[debug] [2025-07-15T01:06:44.593Z] [unzip] Entry: client/assets/extensions/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-15T01:06:44.593Z] [unzip] Processing entry: client\assets\extensions\
[debug] [2025-07-15T01:06:44.593Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\extensions\
[debug] [2025-07-15T01:06:44.594Z] [unzip] Entry: client/assets/extensions/default-extension.png (compressed_size=1646 bytes, uncompressed_size=1657 bytes)
[debug] [2025-07-15T01:06:44.594Z] [unzip] Processing entry: client\assets\extensions\default-extension.png
[debug] [2025-07-15T01:06:44.594Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\extensions
[debug] [2025-07-15T01:06:44.594Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\extensions\default-extension.png
[debug] [2025-07-15T01:06:44.595Z] [unzip] Entry: client/assets/img/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-15T01:06:44.595Z] [unzip] Processing entry: client\assets\img\
[debug] [2025-07-15T01:06:44.595Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\img\
[debug] [2025-07-15T01:06:44.596Z] [unzip] Entry: client/assets/img/database.png (compressed_size=29449 bytes, uncompressed_size=29988 bytes)
[debug] [2025-07-15T01:06:44.596Z] [unzip] Processing entry: client\assets\img\database.png
[debug] [2025-07-15T01:06:44.596Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\img
[debug] [2025-07-15T01:06:44.596Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\img\database.png
[debug] [2025-07-15T01:06:44.597Z] [unzip] Entry: client/assets/provider-icons/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-15T01:06:44.597Z] [unzip] Processing entry: client\assets\provider-icons\
[debug] [2025-07-15T01:06:44.597Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\
[debug] [2025-07-15T01:06:44.598Z] [unzip] Entry: client/assets/provider-icons/auth_service_saml.svg (compressed_size=575 bytes, uncompressed_size=1226 bytes)
[debug] [2025-07-15T01:06:44.598Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_saml.svg
[debug] [2025-07-15T01:06:44.598Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.598Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_saml.svg
[debug] [2025-07-15T01:06:44.599Z] [unzip] Entry: client/assets/provider-icons/auth_service_phone.svg (compressed_size=261 bytes, uncompressed_size=414 bytes)
[debug] [2025-07-15T01:06:44.599Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_phone.svg
[debug] [2025-07-15T01:06:44.599Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.599Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_phone.svg
[debug] [2025-07-15T01:06:44.600Z] [unzip] Entry: client/assets/provider-icons/auth_service_facebook.svg (compressed_size=289 bytes, uncompressed_size=457 bytes)
[debug] [2025-07-15T01:06:44.600Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_facebook.svg
[debug] [2025-07-15T01:06:44.600Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.600Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_facebook.svg
[debug] [2025-07-15T01:06:44.601Z] [unzip] Entry: client/assets/provider-icons/auth_service_game_center.svg (compressed_size=991 bytes, uncompressed_size=3921 bytes)
[debug] [2025-07-15T01:06:44.601Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_game_center.svg
[debug] [2025-07-15T01:06:44.601Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.601Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_game_center.svg
[debug] [2025-07-15T01:06:44.602Z] [unzip] Entry: client/assets/provider-icons/auth_service_apple.svg (compressed_size=230 bytes, uncompressed_size=334 bytes)
[debug] [2025-07-15T01:06:44.602Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_apple.svg
[debug] [2025-07-15T01:06:44.602Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.602Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_apple.svg
[debug] [2025-07-15T01:06:44.603Z] [unzip] Entry: client/assets/provider-icons/auth_service_github.svg (compressed_size=466 bytes, uncompressed_size=838 bytes)
[debug] [2025-07-15T01:06:44.603Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_github.svg
[debug] [2025-07-15T01:06:44.603Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.603Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_github.svg
[debug] [2025-07-15T01:06:44.604Z] [unzip] Entry: client/assets/provider-icons/auth_service_mslive.svg (compressed_size=203 bytes, uncompressed_size=378 bytes)
[debug] [2025-07-15T01:06:44.604Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_mslive.svg
[debug] [2025-07-15T01:06:44.604Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.604Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_mslive.svg
[debug] [2025-07-15T01:06:44.605Z] [unzip] Entry: client/assets/provider-icons/auth_service_yahoo.svg (compressed_size=577 bytes, uncompressed_size=1182 bytes)
[debug] [2025-07-15T01:06:44.605Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_yahoo.svg
[debug] [2025-07-15T01:06:44.605Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.605Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_yahoo.svg
[debug] [2025-07-15T01:06:44.606Z] [unzip] Entry: client/assets/provider-icons/auth_service_twitter.svg (compressed_size=444 bytes, uncompressed_size=751 bytes)
[debug] [2025-07-15T01:06:44.606Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_twitter.svg
[debug] [2025-07-15T01:06:44.606Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.607Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_twitter.svg
[debug] [2025-07-15T01:06:44.608Z] [unzip] Entry: client/assets/provider-icons/auth_service_play_games.svg (compressed_size=565 bytes, uncompressed_size=1173 bytes)
[debug] [2025-07-15T01:06:44.608Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_play_games.svg
[debug] [2025-07-15T01:06:44.608Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.608Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_play_games.svg
[debug] [2025-07-15T01:06:44.609Z] [unzip] Entry: client/assets/provider-icons/auth_service_email.svg (compressed_size=228 bytes, uncompressed_size=326 bytes)
[debug] [2025-07-15T01:06:44.609Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_email.svg
[debug] [2025-07-15T01:06:44.610Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.610Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_email.svg
[debug] [2025-07-15T01:06:44.611Z] [unzip] Entry: client/assets/provider-icons/auth_service_google.svg (compressed_size=409 bytes, uncompressed_size=720 bytes)
[debug] [2025-07-15T01:06:44.611Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_google.svg
[debug] [2025-07-15T01:06:44.611Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.611Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_google.svg
[debug] [2025-07-15T01:06:44.612Z] [unzip] Entry: client/assets/provider-icons/auth_service_oidc.svg (compressed_size=414 bytes, uncompressed_size=858 bytes)
[debug] [2025-07-15T01:06:44.612Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_oidc.svg
[debug] [2025-07-15T01:06:44.612Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-15T01:06:44.612Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_oidc.svg
[debug] [2025-07-15T01:06:44.613Z] [unzip] Entry: client/browserconfig.xml (compressed_size=491 bytes, uncompressed_size=822 bytes)
[debug] [2025-07-15T01:06:44.613Z] [unzip] Processing entry: client\browserconfig.xml
[debug] [2025-07-15T01:06:44.613Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.613Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\browserconfig.xml
[debug] [2025-07-15T01:06:44.614Z] [unzip] Entry: client/favicon-32x32.png (compressed_size=475 bytes, uncompressed_size=475 bytes)
[debug] [2025-07-15T01:06:44.614Z] [unzip] Processing entry: client\favicon-32x32.png
[debug] [2025-07-15T01:06:44.614Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-15T01:06:44.614Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\favicon-32x32.png
[debug] [2025-07-15T01:06:44.615Z] [unzip] Entry: server/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-15T01:06:44.615Z] [unzip] Processing entry: server\
[debug] [2025-07-15T01:06:44.615Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server\
[debug] [2025-07-15T01:06:44.615Z] [unzip] Entry: server/server.mjs.map (compressed_size=217845 bytes, uncompressed_size=944978 bytes)
[debug] [2025-07-15T01:06:44.615Z] [unzip] Processing entry: server\server.mjs.map
[debug] [2025-07-15T01:06:44.616Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server
[debug] [2025-07-15T01:06:44.616Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server\server.mjs.map
[debug] [2025-07-15T01:06:44.620Z] [unzip] Entry: server/server.mjs (compressed_size=276407 bytes, uncompressed_size=933701 bytes)
[debug] [2025-07-15T01:06:44.621Z] [unzip] Processing entry: server\server.mjs
[debug] [2025-07-15T01:06:44.621Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server
[debug] [2025-07-15T01:06:44.621Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server\server.mjs
[info] i  functions: Watching "C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\" for Cloud Functions..."}}
[debug] [2025-07-15T01:06:44.637Z] Validating nodejs source
[debug] [2025-07-15T01:06:46.178Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:06:46.179Z] Building nodejs source
[debug] [2025-07-15T01:06:46.179Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:06:46.181Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:06:46.191Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8841

[debug] [2025-07-15T01:06:51.346Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[info] +  functions[us-central1-chatCompletion]: http function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletion). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletion)."}}
[info] +  functions[us-central1-chatCompletionStream]: http function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletionStream). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/rafthoria/us-central1/chatCompletionStream)."}}
[info] +  functions[us-central1-testAIEndpoint]: http function initialized (http://127.0.0.1:5001/rafthoria/us-central1/testAIEndpoint). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/rafthoria/us-central1/testAIEndpoint)."}}
[debug] [2025-07-15T01:06:55.393Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-15T01:13:19.314Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\index.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\index.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:13:20.330Z] Validating nodejs source
[debug] [2025-07-15T01:13:21.646Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:13:21.646Z] Building nodejs source
[debug] [2025-07-15T01:13:21.647Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:13:21.648Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:13:21.663Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8871

[debug] [2025-07-15T01:13:23.209Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:13:30.212Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\services\aiService.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\services\\aiService.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:13:31.213Z] Validating nodejs source
[debug] [2025-07-15T01:13:32.210Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:13:32.210Z] Building nodejs source
[debug] [2025-07-15T01:13:32.210Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:13:32.211Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:13:32.221Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8869

[debug] [2025-07-15T01:13:33.205Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:13:49.845Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\services\aiService.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\services\\aiService.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:13:50.855Z] Validating nodejs source
[debug] [2025-07-15T01:13:51.892Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:13:51.892Z] Building nodejs source
[debug] [2025-07-15T01:13:51.892Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:13:51.893Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:13:51.905Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8068

[debug] [2025-07-15T01:13:52.880Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:14:01.655Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\services\aiService.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\services\\aiService.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:14:02.661Z] Validating nodejs source
[debug] [2025-07-15T01:14:03.626Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:14:03.626Z] Building nodejs source
[debug] [2025-07-15T01:14:03.626Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:14:03.627Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:14:03.636Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8152

[debug] [2025-07-15T01:14:04.585Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:14:12.625Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\services\aiService.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\services\\aiService.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:14:13.637Z] Validating nodejs source
[debug] [2025-07-15T01:14:14.606Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:14:14.606Z] Building nodejs source
[debug] [2025-07-15T01:14:14.606Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:14:14.608Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:14:14.618Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8581

[debug] [2025-07-15T01:14:15.527Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:14:30.315Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\index.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\index.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:14:31.327Z] Validating nodejs source
[debug] [2025-07-15T01:14:32.165Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:14:32.165Z] Building nodejs source
[debug] [2025-07-15T01:14:32.165Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:14:32.166Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:14:32.177Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8165

[debug] [2025-07-15T01:14:33.087Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:14:41.669Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\index.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\index.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:14:42.674Z] Validating nodejs source
[debug] [2025-07-15T01:14:43.491Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:14:43.491Z] Building nodejs source
[debug] [2025-07-15T01:14:43.491Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:14:43.492Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:14:43.503Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8657

[debug] [2025-07-15T01:14:44.388Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:14:51.775Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\src\index.ts changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\src\\index.ts changed, reloading triggers"}}
[debug] [2025-07-15T01:14:52.776Z] Validating nodejs source
[debug] [2025-07-15T01:14:53.659Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:14:53.659Z] Building nodejs source
[debug] [2025-07-15T01:14:53.659Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:14:53.660Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:14:53.670Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8162

[debug] [2025-07-15T01:14:54.597Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:19:02.772Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\config\ai-config.json changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\config\\ai-config.json changed, reloading triggers"}}
[debug] [2025-07-15T01:19:02.777Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\prompts\latexInstructions.js.map changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\prompts\\latexInstructions.js.map changed, reloading triggers"}}
[debug] [2025-07-15T01:19:02.777Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\prompts\latexInstructions.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\prompts\\latexInstructions.js changed, reloading triggers"}}
[debug] [2025-07-15T01:19:02.811Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\services\aiService.js.map changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\services\\aiService.js.map changed, reloading triggers"}}
[debug] [2025-07-15T01:19:02.816Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\services\aiService.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\services\\aiService.js changed, reloading triggers"}}
[debug] [2025-07-15T01:19:02.833Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\index.js.map changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\index.js.map changed, reloading triggers"}}
[debug] [2025-07-15T01:19:02.833Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\index.js changed, reloading triggers"}}
[debug] [2025-07-15T01:19:03.849Z] Validating nodejs source
[debug] [2025-07-15T01:19:04.846Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:19:04.847Z] Building nodejs source
[debug] [2025-07-15T01:19:04.847Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:19:04.847Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:19:04.858Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8576

[debug] [2025-07-15T01:19:05.788Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:20:11.224Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\config\ai-config.json changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\config\\ai-config.json changed, reloading triggers"}}
[debug] [2025-07-15T01:20:11.229Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\prompts\latexInstructions.js.map changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\prompts\\latexInstructions.js.map changed, reloading triggers"}}
[debug] [2025-07-15T01:20:11.230Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\prompts\latexInstructions.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\prompts\\latexInstructions.js changed, reloading triggers"}}
[debug] [2025-07-15T01:20:11.273Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\services\aiService.js.map changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\services\\aiService.js.map changed, reloading triggers"}}
[debug] [2025-07-15T01:20:11.274Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\services\aiService.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\services\\aiService.js changed, reloading triggers"}}
[debug] [2025-07-15T01:20:11.294Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\index.js.map changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\index.js.map changed, reloading triggers"}}
[debug] [2025-07-15T01:20:11.295Z] File C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\lib\index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\lib\\index.js changed, reloading triggers"}}
[debug] [2025-07-15T01:20:12.298Z] Validating nodejs source
[debug] [2025-07-15T01:20:13.960Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint \"src/**/*.{ts,js}\"",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "@eslint/eslintrc": "^3.1.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "type": "module",
  "private": true
}
[debug] [2025-07-15T01:20:13.960Z] Building nodejs source
[debug] [2025-07-15T01:20:13.960Z] Failed to find version of module node: reached end of search path C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-15T01:20:13.961Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-15T01:20:13.973Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\Rafthor\rafthoria\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8004

[debug] [2025-07-15T01:20:15.222Z] Got response from /__/functions.yaml {"endpoints":{"chatCompletion":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletion"},"chatCompletionStream":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"chatCompletionStream"},"testAIEndpoint":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"testAIEndpoint"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: chatCompletion, chatCompletionStream, testAIEndpoint."}}
[debug] [2025-07-15T01:20:37.531Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:20:37.531Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:20:37.531Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:20:37.531Z"],"workRunningCount":1}
[debug] [2025-07-15T01:20:37.531Z] Accepted request OPTIONS /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-15T01:20:37.534Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-15T01:20:37.534Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-15T01:20:37.548Z] [worker-pool] addWorker(us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(us-central1-chatCompletion)"}}
[debug] [2025-07-15T01:20:37.549Z] [worker-pool] Adding worker with key us-central1-chatCompletion, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key us-central1-chatCompletion, total=1"}}
[debug] [2025-07-15T01:20:38.892Z] [runtime-status] [12272] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-15T01:20:38.893Z] [runtime-status] [12272] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-15T01:20:38.893Z] [runtime-status] [12272] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-15T01:20:38.894Z] [runtime-status] [12272] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-15T01:20:39.335Z] [runtime-status] [12272] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-15T01:20:39.335Z] [runtime-status] [12272] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-15T01:20:39.336Z] [runtime-status] [12272] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-15T01:20:39.363Z] [runtime-status] [12272] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-15T01:20:39.382Z] [runtime-status] [12272] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-15T01:20:39.383Z] [runtime-status] [12272] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[debug] [2025-07-15T01:20:39.705Z] [runtime-status] [12272] Functions runtime initialized. {"cwd":"C:\\Users\\<USER>\\Desktop\\Rafthor\\rafthoria\\functions","node_version":"22.14.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Functions runtime initialized. {\"cwd\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\rafthoria\\\\functions\",\"node_version\":\"22.14.0\"}"}}
[debug] [2025-07-15T01:20:39.706Z] [runtime-status] [12272] Listening to port: \\?\pipe\fire_emu_aef7f6fa2c236300 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[runtime-status] [12272] Listening to port: \\\\?\\pipe\\fire_emu_aef7f6fa2c236300"}}
[debug] [2025-07-15T01:20:39.720Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:20:39.721Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-15T01:20:39.722Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY"}}
[debug] [2025-07-15T01:20:39.732Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 9.9921ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 9.9921ms"}}
[debug] [2025-07-15T01:20:39.732Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:20:39.732Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-15T01:20:39.732Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-15T01:20:39.732Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:20:39.741Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:20:39.741Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:20:39.742Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:20:39.741Z"],"workRunningCount":1}
[debug] [2025-07-15T01:20:39.742Z] Accepted request POST /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-15T01:20:39.743Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-15T01:20:39.743Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-15T01:20:39.743Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-15T01:20:39.743Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY"}}
[info] >  {"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"} {"user":{"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"verifications\":{\"app\":\"MISSING\",\"auth\":\"VALID\"},\"logging.googleapis.com/labels\":{\"firebase-log-type\":\"callable-request-verification\"},\"severity\":\"DEBUG\",\"message\":\"Callable request verification passed\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":1,"model":"openai/gpt-4.1-nano","hasAttachments":false,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":1,"model":"openai/gpt-4.1-nano","hasAttachments":false,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageCount\":1,\"model\":\"openai/gpt-4.1-nano\",\"hasAttachments\":false,\"hasMemories\":false,\"memoriesLength\":0,\"severity\":\"INFO\",\"message\":\"Processando requisição de chat com IA\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentLength":13,"severity":"INFO","message":"Mensagem 0 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentLength":13,"severity":"INFO","message":"Mensagem 0 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":0,\"contentLength\":13,\"severity\":\"INFO\",\"message\":\"Mensagem 0 tem conteúdo string\"}"}}
[warn] !  Google API requested!
   - URL: "https://oauth2.googleapis.com/token"
   - Be careful, this may be a production service. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Google API requested!\n   - URL: \"https://oauth2.googleapis.com/token\"\n   - Be careful, this may be a production service."}}
[info] >  {"endpoint":"OpenRouter","model":"openai/gpt-4.1-nano","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"} {"user":{"endpoint":"OpenRouter","model":"openai/gpt-4.1-nano","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpoint\":\"OpenRouter\",\"model\":\"openai/gpt-4.1-nano\",\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"severity\":\"INFO\",\"message\":\"Processando com serviço de IA\"}"}}
[info] >  📝 Adicionando system prompt da conversa: Meu nome é Pedro tenho 12 anos. {"user":"📝 Adicionando system prompt da conversa: Meu nome é Pedro tenho 12 anos.","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 📝 Adicionando system prompt da conversa: Meu nome é Pedro tenho 12 anos."}}
[info] >  🧠 Nenhuma memória encontrada para adicionar ao prompt {"user":"🧠 Nenhuma memória encontrada para adicionar ao prompt","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 🧠 Nenhuma memória encontrada para adicionar ao prompt"}}
[info] >  📝 System prompt final: Meu nome é Pedro tenho 12 anos. {"user":"📝 System prompt final: Meu nome é Pedro tenho 12 anos.","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 📝 System prompt final: Meu nome é Pedro tenho 12 anos."}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","useCoT":false,"messageLength":13,"severity":"INFO","message":"Processando requisição de IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","useCoT":false,"messageLength":13,"severity":"INFO","message":"Processando requisição de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-nano\",\"useCoT\":false,\"messageLength\":13,\"severity\":\"INFO\",\"message\":\"Processando requisição de IA\"}"}}
[info] >  {"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-nano","severity":"INFO","message":"Debug endpoint info"} {"user":{"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-nano","severity":"INFO","message":"Debug endpoint info"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"endpointUrl\":\"https://openrouter.ai/api/v1\",\"model\":\"openai/gpt-4.1-nano\",\"severity\":\"INFO\",\"message\":\"Debug endpoint info\"}"}}
[info] >  {"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"} {"user":{"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"detectedProvider\":\"openrouter\",\"severity\":\"INFO\",\"message\":\"Debug provider detection\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","usage":{"prompt_tokens":23,"completion_tokens":5,"total_tokens":28,"cost":0.000004257,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":70,"processingTime":1884,"severity":"INFO","message":"Chat completion com IA bem-sucedido"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","usage":{"prompt_tokens":23,"completion_tokens":5,"total_tokens":28,"cost":0.000004257,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":70,"processingTime":1884,"severity":"INFO","message":"Chat completion com IA bem-sucedido"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-nano\",\"usage\":{\"prompt_tokens\":23,\"completion_tokens\":5,\"total_tokens\":28,\"cost\":0.000004257,\"is_byok\":false,\"prompt_tokens_details\":{\"cached_tokens\":0},\"cost_details\":{\"upstream_inference_cost\":null},\"completion_tokens_details\":{\"reasoning_tokens\":0}},\"usedCoT\":false,\"confidence\":70,\"processingTime\":1884,\"severity\":\"INFO\",\"message\":\"Chat completion com IA bem-sucedido\"}"}}
[debug] [2025-07-15T01:20:42.496Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 2752.4367ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 2752.4367ms"}}
[debug] [2025-07-15T01:20:42.496Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:20:42.496Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-15T01:20:42.496Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-15T01:20:42.496Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:21:04.414Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:21:04.414Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:21:04.415Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:21:04.414Z"],"workRunningCount":1}
[debug] [2025-07-15T01:21:04.415Z] Accepted request OPTIONS /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-15T01:21:04.417Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-15T01:21:04.417Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-15T01:21:04.417Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-15T01:21:04.418Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY"}}
[debug] [2025-07-15T01:21:04.421Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 3.2969ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 3.2969ms"}}
[debug] [2025-07-15T01:21:04.421Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:21:04.421Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-15T01:21:04.422Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-15T01:21:04.422Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:21:04.431Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:21:04.431Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:21:04.431Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:21:04.431Z"],"workRunningCount":1}
[debug] [2025-07-15T01:21:04.431Z] Accepted request POST /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-15T01:21:04.432Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-15T01:21:04.432Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-15T01:21:04.432Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-15T01:21:04.433Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY"}}
[info] >  {"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"} {"user":{"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"verifications\":{\"app\":\"MISSING\",\"auth\":\"VALID\"},\"logging.googleapis.com/labels\":{\"firebase-log-type\":\"callable-request-verification\"},\"severity\":\"DEBUG\",\"message\":\"Callable request verification passed\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":1,"model":"openai/gpt-4.1-nano","hasAttachments":false,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":1,"model":"openai/gpt-4.1-nano","hasAttachments":false,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageCount\":1,\"model\":\"openai/gpt-4.1-nano\",\"hasAttachments\":false,\"hasMemories\":false,\"memoriesLength\":0,\"severity\":\"INFO\",\"message\":\"Processando requisição de chat com IA\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentLength":13,"severity":"INFO","message":"Mensagem 0 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentLength":13,"severity":"INFO","message":"Mensagem 0 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":0,\"contentLength\":13,\"severity\":\"INFO\",\"message\":\"Mensagem 0 tem conteúdo string\"}"}}
[info] >  {"endpoint":"OpenRouter","model":"openai/gpt-4.1-nano","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"} {"user":{"endpoint":"OpenRouter","model":"openai/gpt-4.1-nano","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpoint\":\"OpenRouter\",\"model\":\"openai/gpt-4.1-nano\",\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"severity\":\"INFO\",\"message\":\"Processando com serviço de IA\"}"}}
[info] >  📝 Adicionando system prompt da conversa: Meu nome é Pedro tenho 12 anos. {"user":"📝 Adicionando system prompt da conversa: Meu nome é Pedro tenho 12 anos.","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 📝 Adicionando system prompt da conversa: Meu nome é Pedro tenho 12 anos."}}
[info] >  🧠 Nenhuma memória encontrada para adicionar ao prompt {"user":"🧠 Nenhuma memória encontrada para adicionar ao prompt","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 🧠 Nenhuma memória encontrada para adicionar ao prompt"}}
[info] >  📝 System prompt final: Meu nome é Pedro tenho 12 anos. {"user":"📝 System prompt final: Meu nome é Pedro tenho 12 anos.","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 📝 System prompt final: Meu nome é Pedro tenho 12 anos."}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","useCoT":false,"messageLength":13,"severity":"INFO","message":"Processando requisição de IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","useCoT":false,"messageLength":13,"severity":"INFO","message":"Processando requisição de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-nano\",\"useCoT\":false,\"messageLength\":13,\"severity\":\"INFO\",\"message\":\"Processando requisição de IA\"}"}}
[info] >  {"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-nano","severity":"INFO","message":"Debug endpoint info"} {"user":{"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-nano","severity":"INFO","message":"Debug endpoint info"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"endpointUrl\":\"https://openrouter.ai/api/v1\",\"model\":\"openai/gpt-4.1-nano\",\"severity\":\"INFO\",\"message\":\"Debug endpoint info\"}"}}
[info] >  {"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"} {"user":{"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"detectedProvider\":\"openrouter\",\"severity\":\"INFO\",\"message\":\"Debug provider detection\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","usage":{"prompt_tokens":23,"completion_tokens":5,"total_tokens":28,"cost":0.000004257,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":70,"processingTime":1751,"severity":"INFO","message":"Chat completion com IA bem-sucedido"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-nano","usage":{"prompt_tokens":23,"completion_tokens":5,"total_tokens":28,"cost":0.000004257,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":70,"processingTime":1751,"severity":"INFO","message":"Chat completion com IA bem-sucedido"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-nano\",\"usage\":{\"prompt_tokens\":23,\"completion_tokens\":5,\"total_tokens\":28,\"cost\":0.000004257,\"is_byok\":false,\"prompt_tokens_details\":{\"cached_tokens\":0},\"cost_details\":{\"upstream_inference_cost\":null},\"completion_tokens_details\":{\"reasoning_tokens\":0}},\"usedCoT\":false,\"confidence\":70,\"processingTime\":1751,\"severity\":\"INFO\",\"message\":\"Chat completion com IA bem-sucedido\"}"}}
[debug] [2025-07-15T01:21:06.297Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 1864.8005ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 1864.8005ms"}}
[debug] [2025-07-15T01:21:06.298Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:21:06.298Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-15T01:21:06.298Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-15T01:21:06.298Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:59:47.109Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:59:47.109Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:59:47.110Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:59:47.109Z"],"workRunningCount":1}
[debug] [2025-07-15T01:59:47.110Z] Accepted request OPTIONS /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-15T01:59:47.113Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-15T01:59:47.113Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-15T01:59:47.113Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-15T01:59:47.114Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY"}}
[debug] [2025-07-15T01:59:47.117Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 3.8921ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 3.8921ms"}}
[debug] [2025-07-15T01:59:47.118Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:59:47.118Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-15T01:59:47.118Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-15T01:59:47.118Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:59:47.129Z] [work-queue] {"queuedWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:59:47.129Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-15T01:59:47.129Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/rafthoria/us-central1/chatCompletion-2025-07-15T01:59:47.129Z"],"workRunningCount":1}
[debug] [2025-07-15T01:59:47.130Z] Accepted request POST /rafthoria/us-central1/chatCompletion --> us-central1-chatCompletion
[debug] [2025-07-15T01:59:47.132Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-15T01:59:47.132Z] [functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/ {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/rafthoria/us-central1/chatCompletion, mapping to path=/"}}
[debug] [2025-07-15T01:59:47.132Z] [worker-pool] submitRequest(triggerId=us-central1-chatCompletion) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-chatCompletion)"}}
[info] i  functions: Beginning execution of "us-central1-chatCompletion" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Beginning execution of \"us-central1-chatCompletion\""}}
[debug] [2025-07-15T01:59:47.133Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: BUSY"}}
[info] >  {"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"} {"user":{"verifications":{"app":"MISSING","auth":"VALID"},"logging.googleapis.com/labels":{"firebase-log-type":"callable-request-verification"},"severity":"DEBUG","message":"Callable request verification passed"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"verifications\":{\"app\":\"MISSING\",\"auth\":\"VALID\"},\"logging.googleapis.com/labels\":{\"firebase-log-type\":\"callable-request-verification\"},\"severity\":\"DEBUG\",\"message\":\"Callable request verification passed\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":25,"model":"openai/gpt-4.1-mini","hasAttachments":true,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageCount":25,"model":"openai/gpt-4.1-mini","hasAttachments":true,"hasMemories":false,"memoriesLength":0,"severity":"INFO","message":"Processando requisição de chat com IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageCount\":25,\"model\":\"openai/gpt-4.1-mini\",\"hasAttachments\":true,\"hasMemories\":false,\"memoriesLength\":0,\"severity\":\"INFO\",\"message\":\"Processando requisição de chat com IA\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Me dê emojis variados e não repetidos e que fazem sentido serem colocado para cada canal e categoria deste discord. Ele é para guardar materiais."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752346588183-z1lbqhyim.png?alt=media&token=34a4d735-e55b-4587-a4bf-f3fdad393d9e"}}],"severity":"INFO","message":"Mensagem 0 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":0,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Me dê emojis variados e não repetidos e que fazem sentido serem colocado para cada canal e categoria deste discord. Ele é para guardar materiais."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752346588183-z1lbqhyim.png?alt=media&token=34a4d735-e55b-4587-a4bf-f3fdad393d9e"}}],"severity":"INFO","message":"Mensagem 0 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":0,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Me dê emojis variados e não repetidos e que fazem sentido serem colocado para cada canal e categoria deste discord. Ele é para guardar materiais.\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752346588183-z1lbqhyim.png?alt=media&token=34a4d735-e55b-4587-a4bf-f3fdad393d9e\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 0 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":1,"contentLength":1139,"severity":"INFO","message":"Mensagem 1 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":1,"contentLength":1139,"severity":"INFO","message":"Mensagem 1 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":1,\"contentLength\":1139,\"severity\":\"INFO\",\"message\":\"Mensagem 1 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":2,"contentLength":27,"severity":"INFO","message":"Mensagem 2 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":2,"contentLength":27,"severity":"INFO","message":"Mensagem 2 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":2,\"contentLength\":27,\"severity\":\"INFO\",\"message\":\"Mensagem 2 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":3,"contentLength":997,"severity":"INFO","message":"Mensagem 3 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":3,"contentLength":997,"severity":"INFO","message":"Mensagem 3 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":3,\"contentLength\":997,\"severity\":\"INFO\",\"message\":\"Mensagem 3 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":4,"contentLength":49,"severity":"INFO","message":"Mensagem 4 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":4,"contentLength":49,"severity":"INFO","message":"Mensagem 4 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":4,\"contentLength\":49,\"severity\":\"INFO\",\"message\":\"Mensagem 4 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":5,"contentLength":1067,"severity":"INFO","message":"Mensagem 5 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":5,"contentLength":1067,"severity":"INFO","message":"Mensagem 5 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":5,\"contentLength\":1067,\"severity\":\"INFO\",\"message\":\"Mensagem 5 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":6,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça para agora todos esses chats, repetindo, sem repetir nenhum já usado antes."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752347368212-bvj9b2u6b.png?alt=media&token=dda909a0-42a5-4ab5-bb59-d8f488c8f387"}}],"severity":"INFO","message":"Mensagem 6 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":6,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça para agora todos esses chats, repetindo, sem repetir nenhum já usado antes."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752347368212-bvj9b2u6b.png?alt=media&token=dda909a0-42a5-4ab5-bb59-d8f488c8f387"}}],"severity":"INFO","message":"Mensagem 6 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":6,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Faça para agora todos esses chats, repetindo, sem repetir nenhum já usado antes.\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752347368212-bvj9b2u6b.png?alt=media&token=dda909a0-42a5-4ab5-bb59-d8f488c8f387\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 6 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":7,"contentLength":635,"severity":"INFO","message":"Mensagem 7 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":7,"contentLength":635,"severity":"INFO","message":"Mensagem 7 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":7,\"contentLength\":635,\"severity\":\"INFO\",\"message\":\"Mensagem 7 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":8,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça agora para esses:"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752348648021-ndayjj70u.png?alt=media&token=71e8696e-7755-48d3-973b-3d53b945888a"}}],"severity":"INFO","message":"Mensagem 8 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":8,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça agora para esses:"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752348648021-ndayjj70u.png?alt=media&token=71e8696e-7755-48d3-973b-3d53b945888a"}}],"severity":"INFO","message":"Mensagem 8 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":8,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Faça agora para esses:\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752348648021-ndayjj70u.png?alt=media&token=71e8696e-7755-48d3-973b-3d53b945888a\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 8 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":9,"contentLength":751,"severity":"INFO","message":"Mensagem 9 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":9,"contentLength":751,"severity":"INFO","message":"Mensagem 9 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":9,\"contentLength\":751,\"severity\":\"INFO\",\"message\":\"Mensagem 9 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":10,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça agora para esses"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349070467-ovzg5v5n5.png?alt=media&token=e14d86dd-87d7-4749-acfb-fd409133b62d"}}],"severity":"INFO","message":"Mensagem 10 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":10,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça agora para esses"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349070467-ovzg5v5n5.png?alt=media&token=e14d86dd-87d7-4749-acfb-fd409133b62d"}}],"severity":"INFO","message":"Mensagem 10 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":10,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Faça agora para esses\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349070467-ovzg5v5n5.png?alt=media&token=e14d86dd-87d7-4749-acfb-fd409133b62d\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 10 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":11,"contentLength":236,"severity":"INFO","message":"Mensagem 11 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":11,"contentLength":236,"severity":"INFO","message":"Mensagem 11 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":11,\"contentLength\":236,\"severity\":\"INFO\",\"message\":\"Mensagem 11 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":12,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça agora para isto."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349234450-ub43s66wu.png?alt=media&token=5698a637-ddfd-49cc-874a-5cfffba942cc"}}],"severity":"INFO","message":"Mensagem 12 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":12,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça agora para isto."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349234450-ub43s66wu.png?alt=media&token=5698a637-ddfd-49cc-874a-5cfffba942cc"}}],"severity":"INFO","message":"Mensagem 12 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":12,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Faça agora para isto.\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349234450-ub43s66wu.png?alt=media&token=5698a637-ddfd-49cc-874a-5cfffba942cc\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 12 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":13,"contentLength":791,"severity":"INFO","message":"Mensagem 13 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":13,"contentLength":791,"severity":"INFO","message":"Mensagem 13 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":13,\"contentLength\":791,\"severity\":\"INFO\",\"message\":\"Mensagem 13 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":14,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Para este agora"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349385183-r2ulgvndk.png?alt=media&token=5be5914a-2057-40f1-ba06-ff53dce43f4e"}}],"severity":"INFO","message":"Mensagem 14 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":14,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Para este agora"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349385183-r2ulgvndk.png?alt=media&token=5be5914a-2057-40f1-ba06-ff53dce43f4e"}}],"severity":"INFO","message":"Mensagem 14 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":14,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Para este agora\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349385183-r2ulgvndk.png?alt=media&token=5be5914a-2057-40f1-ba06-ff53dce43f4e\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 14 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":15,"contentLength":755,"severity":"INFO","message":"Mensagem 15 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":15,"contentLength":755,"severity":"INFO","message":"Mensagem 15 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":15,\"contentLength\":755,\"severity\":\"INFO\",\"message\":\"Mensagem 15 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":16,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Agora esses"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349879787-admktx3zo.png?alt=media&token=3a4fa668-2ad4-47c1-95b9-345bce5e4236"}}],"severity":"INFO","message":"Mensagem 16 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":16,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Agora esses"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349879787-admktx3zo.png?alt=media&token=3a4fa668-2ad4-47c1-95b9-345bce5e4236"}}],"severity":"INFO","message":"Mensagem 16 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":16,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Agora esses\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752349879787-admktx3zo.png?alt=media&token=3a4fa668-2ad4-47c1-95b9-345bce5e4236\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 16 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":17,"contentLength":643,"severity":"INFO","message":"Mensagem 17 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":17,"contentLength":643,"severity":"INFO","message":"Mensagem 17 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":17,\"contentLength\":643,\"severity\":\"INFO\",\"message\":\"Mensagem 17 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":18,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Agora para esses"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350122971-kdl49q8fl.png?alt=media&token=584b6c7b-fbfe-4e9b-8af0-37f006e16bdb"}}],"severity":"INFO","message":"Mensagem 18 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":18,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Agora para esses"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350122971-kdl49q8fl.png?alt=media&token=584b6c7b-fbfe-4e9b-8af0-37f006e16bdb"}}],"severity":"INFO","message":"Mensagem 18 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":18,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Agora para esses\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350122971-kdl49q8fl.png?alt=media&token=584b6c7b-fbfe-4e9b-8af0-37f006e16bdb\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 18 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":19,"contentLength":890,"severity":"INFO","message":"Mensagem 19 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":19,"contentLength":890,"severity":"INFO","message":"Mensagem 19 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":19,\"contentLength\":890,\"severity\":\"INFO\",\"message\":\"Mensagem 19 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":20,"contentLength":37,"severity":"INFO","message":"Mensagem 20 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":20,"contentLength":37,"severity":"INFO","message":"Mensagem 20 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":20,\"contentLength\":37,\"severity\":\"INFO\",\"message\":\"Mensagem 20 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":21,"contentLength":18,"severity":"INFO","message":"Mensagem 21 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":21,"contentLength":18,"severity":"INFO","message":"Mensagem 21 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":21,\"contentLength\":18,\"severity\":\"INFO\",\"message\":\"Mensagem 21 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":22,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Por fim, para esses. E obrigado pela ajuda."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350613138-ttze22m73.png?alt=media&token=e2b729e4-49fc-4900-86f9-92e7d2430f13"}}],"severity":"INFO","message":"Mensagem 22 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":22,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Por fim, para esses. E obrigado pela ajuda."},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350613138-ttze22m73.png?alt=media&token=e2b729e4-49fc-4900-86f9-92e7d2430f13"}}],"severity":"INFO","message":"Mensagem 22 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":22,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Por fim, para esses. E obrigado pela ajuda.\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350613138-ttze22m73.png?alt=media&token=e2b729e4-49fc-4900-86f9-92e7d2430f13\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 22 tem conteúdo multimodal\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":23,"contentLength":327,"severity":"INFO","message":"Mensagem 23 tem conteúdo string"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":23,"contentLength":327,"severity":"INFO","message":"Mensagem 23 tem conteúdo string"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":23,\"contentLength\":327,\"severity\":\"INFO\",\"message\":\"Mensagem 23 tem conteúdo string\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":24,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça desse"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350122971-kdl49q8fl.png?alt=media&token=584b6c7b-fbfe-4e9b-8af0-37f006e16bdb"}}],"severity":"INFO","message":"Mensagem 24 tem conteúdo multimodal"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","messageIndex":24,"contentTypes":["text","image_url"],"contentCount":2,"fullContent":[{"type":"text","text":"Faça desse"},{"type":"image_url","image_url":{"url":"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350122971-kdl49q8fl.png?alt=media&token=584b6c7b-fbfe-4e9b-8af0-37f006e16bdb"}}],"severity":"INFO","message":"Mensagem 24 tem conteúdo multimodal"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"messageIndex\":24,\"contentTypes\":[\"text\",\"image_url\"],\"contentCount\":2,\"fullContent\":[{\"type\":\"text\",\"text\":\"Faça desse\"},{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://firebasestorage.googleapis.com/v0/b/rafthoria.firebasestorage.app/o/usuarios%2FsR8nztp7h5YWDazOdDBlt4k166k2%2Fconversas%2FbRog0GU3lqRw5JOAomGg%2Fanexos%2Fattachment-1752350122971-kdl49q8fl.png?alt=media&token=584b6c7b-fbfe-4e9b-8af0-37f006e16bdb\"}}],\"severity\":\"INFO\",\"message\":\"Mensagem 24 tem conteúdo multimodal\"}"}}
[info] >  {"endpoint":"OpenRouter","model":"openai/gpt-4.1-mini","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"} {"user":{"endpoint":"OpenRouter","model":"openai/gpt-4.1-mini","userId":"sR8nztp7h5YWDazOdDBlt4k166k2","severity":"INFO","message":"Processando com serviço de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpoint\":\"OpenRouter\",\"model\":\"openai/gpt-4.1-mini\",\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"severity\":\"INFO\",\"message\":\"Processando com serviço de IA\"}"}}
[info] >  🧠 Nenhuma memória encontrada para adicionar ao prompt {"user":"🧠 Nenhuma memória encontrada para adicionar ao prompt","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 🧠 Nenhuma memória encontrada para adicionar ao prompt"}}
[info] >  📝 System prompt final:  {"user":"📝 System prompt final: ","metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m 📝 System prompt final: "}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-mini","useCoT":false,"messageLength":2,"severity":"INFO","message":"Processando requisição de IA"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-mini","useCoT":false,"messageLength":2,"severity":"INFO","message":"Processando requisição de IA"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-mini\",\"useCoT\":false,\"messageLength\":2,\"severity\":\"INFO\",\"message\":\"Processando requisição de IA\"}"}}
[info] >  {"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-mini","severity":"INFO","message":"Debug endpoint info"} {"user":{"endpointName":"OpenRouter","endpointUrl":"https://openrouter.ai/api/v1","model":"openai/gpt-4.1-mini","severity":"INFO","message":"Debug endpoint info"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"endpointUrl\":\"https://openrouter.ai/api/v1\",\"model\":\"openai/gpt-4.1-mini\",\"severity\":\"INFO\",\"message\":\"Debug endpoint info\"}"}}
[info] >  {"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"} {"user":{"endpointName":"OpenRouter","detectedProvider":"openrouter","severity":"INFO","message":"Debug provider detection"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"endpointName\":\"OpenRouter\",\"detectedProvider\":\"openrouter\",\"severity\":\"INFO\",\"message\":\"Debug provider detection\"}"}}
[info] >  {"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-mini","usage":{"prompt_tokens":5729,"completion_tokens":374,"total_tokens":6103,"cost":0.0028611,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":85,"processingTime":9673,"severity":"INFO","message":"Chat completion com IA bem-sucedido"} {"user":{"userId":"sR8nztp7h5YWDazOdDBlt4k166k2","model":"openai/gpt-4.1-mini","usage":{"prompt_tokens":5729,"completion_tokens":374,"total_tokens":6103,"cost":0.0028611,"is_byok":false,"prompt_tokens_details":{"cached_tokens":0},"cost_details":{"upstream_inference_cost":null},"completion_tokens_details":{"reasoning_tokens":0}},"usedCoT":false,"confidence":85,"processingTime":9673,"severity":"INFO","message":"Chat completion com IA bem-sucedido"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"userId\":\"sR8nztp7h5YWDazOdDBlt4k166k2\",\"model\":\"openai/gpt-4.1-mini\",\"usage\":{\"prompt_tokens\":5729,\"completion_tokens\":374,\"total_tokens\":6103,\"cost\":0.0028611,\"is_byok\":false,\"prompt_tokens_details\":{\"cached_tokens\":0},\"cost_details\":{\"upstream_inference_cost\":null},\"completion_tokens_details\":{\"reasoning_tokens\":0}},\"usedCoT\":false,\"confidence\":85,\"processingTime\":9673,\"severity\":\"INFO\",\"message\":\"Chat completion com IA bem-sucedido\"}"}}
[debug] [2025-07-15T01:59:57.102Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-chatCompletion" in 9969.2827ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finished \"us-central1-chatCompletion\" in 9969.2827ms"}}
[debug] [2025-07-15T01:59:57.102Z] [worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"[worker-us-central1-chatCompletion-d3c08332-af32-4a12-b73c-a3d5a73b0214]: IDLE"}}
[debug] [2025-07-15T01:59:57.102Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-15T01:59:57.102Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-chatCompletion"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-15T01:59:57.102Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}

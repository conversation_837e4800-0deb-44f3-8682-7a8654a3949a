import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/lib/firebase';
import { AttachmentMetadata, ChatMessage } from '@/lib/types/chat';
import { attachmentService } from '@/lib/attachmentService';
import memoryService from '@/lib/memoryService';

interface MessageContent {
  type: 'text' | 'image_url' | 'file';
  text?: string;
  image_url?: {
    url: string;
  };
  file?: {
    filename: string;
    file_data: string;
  };
}

interface AIChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | MessageContent[];
}

interface AIResponse {
  choices: Array<{
    message: {
      role: 'assistant';
      content: string;
    };
    finish_reason: string;
  }>;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  rafthoria_metadata?: {
    usedCoT: boolean;
    confidence: number;
    processingTime: number;
  };
}

interface UseAIChatOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  latexInstructions?: boolean;
  webSearchEnabled?: boolean;
  chatId?: string; // Para buscar memórias específicas do chat
  systemPrompt?: string;
  context?: string;
}

export const useAIChat = (options: UseAIChatOptions = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Convert attachments to OpenRouter format
  const convertAttachmentsToContent = async (
    text: string,
    attachments?: AttachmentMetadata[]
  ): Promise<string | MessageContent[]> => {
    console.log('convertAttachmentsToContent called with:', { text, attachments });

    if (!attachments || attachments.length === 0) {
      console.log('No attachments, returning text only');
      return text;
    }

    const content: MessageContent[] = [];

    // Add text content if present
    if (text.trim()) {
      content.push({
        type: 'text',
        text: text
      });
    }

    // Process attachments
    for (const attachment of attachments) {
      console.log('Processing attachment:', attachment);

      if (attachment.type === 'image') {
        console.log('Adding image attachment:', attachment.url);
        content.push({
          type: 'image_url',
          image_url: {
            url: attachment.url
          }
        });
      } else if (attachment.type === 'pdf') {
        console.log('Processing PDF attachment:', attachment.filename);

        if (attachment.base64Data) {
          console.log('Using stored base64 data, length:', attachment.base64Data.length);

          const fileContent: MessageContent = {
            type: 'file',
            file: {
              filename: attachment.filename,
              file_data: attachment.base64Data
            }
          };

          console.log('Adding file content:', fileContent);
          content.push(fileContent);
        } else {
          console.error('PDF attachment missing base64Data:', attachment.filename);
          // Skip this attachment if no base64 data
        }
      }
    }

    console.log('Final content array:', content);
    return content.length > 0 ? content : text;
  };

  const sendMessage = async (
    messages: AIChatMessage[],
    customOptions?: Partial<UseAIChatOptions>
  ): Promise<AIResponse | null> => {
    if (!user) {
      setError('Usuário não autenticado');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      // Merge options
      const finalOptions = { ...options, ...customOptions };

      // Buscar memórias ativas para o chat
      let memories = '';
      if (user) {
        try {
          console.log('🔍 Buscando memórias para usuário:', user.uid, 'chat:', finalOptions.chatId);
          const activeMemories = await memoryService.getActiveMemoriesForChat(
            user.uid,
            finalOptions.chatId
          );
          console.log('📋 Memórias encontradas:', activeMemories.length, activeMemories);
          memories = memoryService.formatMemoriesForPrompt(activeMemories);
          console.log('📝 Memórias formatadas:', memories);
        } catch (error) {
          console.warn('Erro ao buscar memórias:', error);
          // Continuar sem memórias em caso de erro
        }
      }

      // Prepare request for Firebase Function
      const requestData = {
        messages,
        model: finalOptions.model,
        temperature: finalOptions.temperature,
        max_tokens: finalOptions.maxTokens,
        latexInstructions: finalOptions.latexInstructions,
        webSearchEnabled: finalOptions.webSearchEnabled,
        memories,
        systemPrompt: finalOptions.systemPrompt || "",
        context: finalOptions.context || "",
      };

      console.log('Calling Firebase Function with:', requestData);
      console.log('User authenticated:', !!user);

      // Call Firebase Function directly (with user context)
      const chatCompletion = httpsCallable(functions, 'chatCompletion');
      const result = await chatCompletion(requestData);

      console.log('Firebase Function result:', result);

      return result.data as AIResponse;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      console.error('Erro no chat AI:', err);

      // Se for erro de autenticação ou função não encontrada, retorna resposta de fallback
      if (errorMessage.includes('unauthenticated') || errorMessage.includes('not found')) {
        console.log('Usando resposta de fallback devido a erro de função');
        return {
          choices: [{
            message: {
              role: 'assistant' as const,
              content: `Olá! Sou a RafthorIA. No momento estou funcionando em modo de demonstração.

Para sua mensagem: "${messages[messages.length - 1]?.content}"

Posso ajudar com:
• Responder perguntas gerais
• Explicar conceitos
• Ajudar com programação
• Análise de dados
• Resolução de problemas

Para ativar todas as funcionalidades de IA, configure um endpoint de IA nas configurações.`
            },
            finish_reason: 'stop'
          }],
          model: 'rafthoria-fallback',
          rafthoria_metadata: {
            usedCoT: false,
            confidence: 50,
            processingTime: 100
          }
        } as AIResponse;
      }

      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const generateResponse = async (
    userMessage: string,
    conversationHistory: ChatMessage[] = [],
    customOptions?: Partial<UseAIChatOptions>
  ): Promise<string | null> => {
    const messages: AIChatMessage[] = [
      ...conversationHistory.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content
      })),
      { role: 'user', content: userMessage }
    ];

    const response = await sendMessage(messages, customOptions);
    
    if (response && response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    }

    return null;
  };

  const generateResponseWithMetadata = async (
    userMessage: string,
    conversationHistory: ChatMessage[] = [],
    customOptions?: Partial<UseAIChatOptions>,
    attachments?: AttachmentMetadata[]
  ): Promise<{
    success: boolean;
    response?: string;
    metadata?: {
      usedCoT: boolean;
      confidence: number;
      processingTime: number;
      model: string;
      usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
      };
    };
  }> => {
    console.log('generateResponseWithMetadata called with:', {
      userMessage,
      attachments,
      attachmentCount: attachments?.length || 0,
      historyLength: conversationHistory.length
    });

    // Process conversation history to convert attachments for all messages
    const processedHistory: AIChatMessage[] = [];
    for (const msg of conversationHistory) {
      if (msg.attachments && msg.attachments.length > 0) {
        console.log(`📜 HISTORY - Processing attachments for ${msg.role} message:`, msg.attachments);
        const processedContent = await convertAttachmentsToContent(msg.content, msg.attachments);
        processedHistory.push({
          role: msg.role as 'system' | 'user' | 'assistant',
          content: processedContent
        });
      } else {
        processedHistory.push({
          role: msg.role as 'system' | 'user' | 'assistant',
          content: msg.content
        });
      }
    }

    // Convert user message with attachments
    console.log('🚀 CURRENT MESSAGE - About to call convertAttachmentsToContent with:', { userMessage, attachments });
    console.log('🚀 CURRENT MESSAGE - Number of attachments for current message:', attachments?.length || 0);
    if (attachments && attachments.length > 0) {
      console.log('🚀 CURRENT MESSAGE - Attachment IDs for current message:', attachments.map(att => att.id));
    }
    const userContent = await convertAttachmentsToContent(userMessage, attachments);
    console.log('🚀 CURRENT MESSAGE - convertAttachmentsToContent returned:', userContent);

    const messages: AIChatMessage[] = [
      ...processedHistory,
      { role: 'user', content: userContent }
    ];

    console.log('Final messages array with processed attachments:', messages);

    const response = await sendMessage(messages, customOptions);

    if (response && response.choices && response.choices.length > 0) {
      return {
        success: true,
        response: response.choices[0].message.content,
        metadata: {
          usedCoT: response.rafthoria_metadata?.usedCoT || false,
          confidence: response.rafthoria_metadata?.confidence || 0,
          processingTime: response.rafthoria_metadata?.processingTime || 0,
          model: response.model,
          usage: response.usage,
        }
      };
    }

    return { success: false };
  };

  return {
    sendMessage,
    generateResponse,
    generateResponseWithMetadata,
    loading,
    error,
    clearError: () => setError(null),
  };
};
